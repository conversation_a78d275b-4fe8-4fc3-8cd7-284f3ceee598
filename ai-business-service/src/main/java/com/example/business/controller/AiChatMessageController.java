package com.example.business.controller;

import com.example.common.entity.dto.AiChatMessageDto;
import com.example.common.entity.vo.AiChatMessageVo;
import com.example.business.service.IAiChatMessageService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/ai/chatMessage")
@RequiredArgsConstructor
public class AiChatMessageController {

    private final IAiChatMessageService iAiChatMessageService;

    /**
     * 根据会话id获取会话列表
     * @param sessionId
     * @return
     */
    @GetMapping("/getChatMessageInfo/{sessionId}")
    public List<AiChatMessageVo> getChatSessionInfo(@PathVariable String sessionId) {
        List<AiChatMessageVo> aiChatSessionList =  iAiChatMessageService.selectBySessionId(sessionId);
        return aiChatSessionList;
    }


    /**
     * 根据会话id获取会话列表(ai回复列表)
     * @param sessionId
     * @return
     */
    @GetMapping("/getChatMessageAiInfo/{sessionId}")
    public List<AiChatMessageVo> getChatMessageAiInfo(@PathVariable String sessionId) {
        List<AiChatMessageVo> aiChatSessionList =  iAiChatMessageService.selectByAiInfo(sessionId);
        return aiChatSessionList;
    }


    /***
     * 保存会话详细
     * @param aiChatMessageDto
     */
    @PostMapping("/save")
    public void save(@RequestBody AiChatMessageDto aiChatMessageDto) {
        iAiChatMessageService.insertAiChatMessage(aiChatMessageDto);
    }


}
