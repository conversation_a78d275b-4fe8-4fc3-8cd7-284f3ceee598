package com.example.ai.feign;

import com.example.common.entity.dto.AiChatMessageDto;
import com.example.common.entity.dto.AiChatSessionDto;
import com.example.common.entity.vo.AiChatMessageVo;
import com.example.common.entity.vo.AiChatSessionVo;
import com.example.ai.dto.ChatResponse;
import com.example.common.result.Result;
import com.example.common.feign.config.FeignConfig;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * Business服务Feign客户端
 * 用于调用ai-business-service的相关接口
 * 使用统一的Feign配置和降级策略
 *
 * <AUTHOR>
 * @date 2025-07-31
 */
@FeignClient(
    name = "business-service",
    configuration = FeignConfig.class,
    fallbackFactory = BusinessFeignFallbackFactory.class
)
public interface BusinessFeign {

    /**
     * 创建新对话会话
     * 调用ai-business-service的/ai/chatMessage/createConversation接口
     *
     * @param aiChatSessionDto 会话创建请求参数
     * @return 创建结果，包装在Result中
     */
    @PostMapping("/ai/chatSession/createConversation")
    Result<AiChatSessionVo> createConversation(@RequestBody AiChatSessionDto aiChatSessionDto);

    /**
     * 更新对话标题
     * 调用ai-business-service的/ai/chatMessage/updateConversation接口
     *
     * @param aiChatSessionDto 会话创建请求参数
     * @return 创建结果，包装在Result中
     */
    @PutMapping("/ai/chatSession/updateConversation")
    Result updateConversation(@RequestBody AiChatSessionDto aiChatSessionDto);

    /**
     * 获取对话列表
     * 调用ai-business-service的/business/conversations接口
     *
     * @return 对话列表
     */
    @GetMapping("/ai/chatSession/conversations")
    List<AiChatSessionVo> getConversations();


    /**
     * 获取会话详细列表信息
     *
     * @param sessionId 会话ID
     * @return 会话详细列表信息
     */
    @GetMapping("/ai/chatMessage/getChatMessageInfo/{sessionId}")
    List<AiChatMessageVo> getChatMessageInfo(@PathVariable String sessionId);





    /**
     * 获取会话详细列表信息(ai回复列表)
     *
     * @param sessionId 会话ID
     * @return 会话详细列表信息
     */
    @GetMapping("/ai/chatMessage/getChatMessageAiInfo/{sessionId}")
    List<AiChatMessageVo> getChatMessageAiInfo(@PathVariable String sessionId);

    /**
     * 保存会话详细列表信息

     * @param aiChatMessageDto
     */
    @PostMapping("/ai/chatMessage/save")
    void saveChatMessage(@RequestBody AiChatMessageDto aiChatMessageDto);
}
